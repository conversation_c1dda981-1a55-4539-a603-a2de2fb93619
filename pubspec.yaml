name: droit
description: "A new Flutter project."
publish_to: 'none'
version: 1.0.0+1
environment:
  sdk: ^3.6.1

dependencies:
  # HTTP client for API requests
  dio: ^5.8.0+1
  # File picker for selecting PDF files
  file_picker: ^10.2.0
  flutter:
    sdk: flutter
  # Localization support
  flutter_launcher_icons: ^0.14.4
  flutter_localizations:
    sdk: flutter
  # PDF viewer widget
  flutter_pdfview: ^1.4.0+1
  # Secure storage for tokens
  flutter_secure_storage: ^9.2.4
  # HTTP client (alternative to dio, use one)
  http: ^1.4.0
  # Parser for MIME types (required for MultipartFile)
  http_parser: ^4.1.2
  # Internationalization and localization
  intl: ^0.20.2
  # Path provider for file system access
  path_provider: ^2.1.5
  # PDF manipulation library
  pdf: ^3.11.3
  # State management
  provider: ^6.1.5
  # Local storage
  shared_preferences: ^2.2.2
  # URL launcher for external links
  url_launcher: ^6.3.1
  # Video player support
  video_player: ^2.10.0
  # YouTube player widget
  youtube_player_flutter: ^9.1.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  # Linting rules
  flutter_lints: ^6.0.0

flutter:
  uses-material-design: true
  assets:
    - assets/fonts/
    - assets/A2020071.pdf
    - assets/A2015007.pdf
    - assets/A1990052.pdf
    - assets/A2024051.pdf
    - assets/rokhsaati_logo.jpg
    - assets/logo.jpg

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/rokhsaati_logo.jpg"
  min_sdk_android: 21
  # Configuration pour Android avec icône adaptative pour éviter la déformation
  adaptive_icon_background: "#FFFFFF"
  adaptive_icon_foreground: "assets/rokhsaati_logo.jpg"
  # Désactiver la génération d'icônes legacy pour forcer l'utilisation des adaptatives
  remove_alpha_ios: true
  # Configuration pour le web
  web:
    generate: true
    image_path: "assets/rokhsaati_logo.jpg"
    background_color: "#FFFFFF"
    theme_color: "#2196F3"
  # Configuration pour Windows
  windows:
    generate: true
    image_path: "assets/rokhsaati_logo.jpg"
    icon_size: 48
  # Configuration pour macOS
  macos:
    generate: true
    image_path: "assets/rokhsaati_logo.jpg"